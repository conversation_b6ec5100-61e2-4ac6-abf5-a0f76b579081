#!/bin/bash

# Backend Deployment Script for SuperUp API
# Deploys to root@************

set -e  # Exit on any error

# Configuration
SERVER_HOST="************"
SERVER_USER="root"
APP_NAME="superup-api"
REMOTE_DIR="/opt/$APP_NAME"
LOCAL_DIST_DIR="./dist"
LOCAL_NODE_MODULES="./node_modules"
LOCAL_PACKAGE_JSON="./package.json"
LOCAL_PACKAGE_LOCK="./package-lock.json"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 Starting deployment of SuperUp API to $SERVER_HOST${NC}"

# Step 1: Build the application
echo -e "${YELLOW}📦 Building application...${NC}"
npm run build

# Step 2: Create deployment package
echo -e "${YELLOW}📋 Creating deployment package...${NC}"
TEMP_DIR=$(mktemp -d)
cp -r $LOCAL_DIST_DIR/* $TEMP_DIR/
cp $LOCAL_PACKAGE_JSON $TEMP_DIR/
cp $LOCAL_PACKAGE_LOCK $TEMP_DIR/

# Step 3: Create remote directory and upload files
echo -e "${YELLOW}📤 Uploading files to server...${NC}"
ssh $SERVER_USER@$SERVER_HOST "mkdir -p $REMOTE_DIR"
rsync -avz --delete $TEMP_DIR/ $SERVER_USER@$SERVER_HOST:$REMOTE_DIR/

# Step 4: Install dependencies and setup on server
echo -e "${YELLOW}⚙️  Setting up application on server...${NC}"
ssh $SERVER_USER@$SERVER_HOST << EOF
    cd $REMOTE_DIR
    
    # Install Node.js if not present
    if ! command -v node &> /dev/null; then
        echo "Installing Node.js..."
        curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
        apt-get install -y nodejs
    fi
    
    # Install PM2 globally if not present
    if ! command -v pm2 &> /dev/null; then
        echo "Installing PM2..."
        npm install -g pm2
    fi
    
    # Install production dependencies
    echo "Installing dependencies..."
    npm ci --only=production
    
    # Stop existing application if running
    pm2 stop $APP_NAME || true
    pm2 delete $APP_NAME || true
    
    # Start application with PM2
    echo "Starting application with PM2..."
    pm2 start ecosystem.config.js --name $APP_NAME
    
    # Save PM2 configuration
    pm2 save
    pm2 startup
    
    echo "Application deployed successfully!"
    pm2 status
EOF

# Cleanup
rm -rf $TEMP_DIR

echo -e "${GREEN}✅ Deployment completed successfully!${NC}"
echo -e "${GREEN}🌐 Your application should be running on the server${NC}"
echo -e "${YELLOW}📊 To check status: ssh $SERVER_USER@$SERVER_HOST 'pm2 status'${NC}"
echo -e "${YELLOW}📋 To view logs: ssh $SERVER_USER@$SERVER_HOST 'pm2 logs $APP_NAME'${NC}"
